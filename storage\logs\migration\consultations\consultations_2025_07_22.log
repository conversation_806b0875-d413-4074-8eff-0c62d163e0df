[2025-07-22 17:43:47] local.INFO: Starting migration command: migratewp:consultations  
[2025-07-22 17:43:47] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 17:43:52] local.INFO: WordPress API connection validated  
[2025-07-22 17:43:52] local.INFO: Database connection validated  
[2025-07-22 17:43:52] local.INFO: Starting consultations migration  
[2025-07-22 17:43:52] local.INFO: Running in DRY RUN mode  
[2025-07-22 17:43:52] local.INFO: Starting consultations migration for clinic 14  
[2025-07-22 17:43:52] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:43:57] local.INFO: Found 546 consultations for clinic 14  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 975 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 976 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 977 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 978 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 972 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 973 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 974 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 962 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 964 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 965 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 966 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 967 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 968 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 969 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 970 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 958 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 959 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 961 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 955 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 956 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 957 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 949 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 950 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 951 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 943 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 944 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 945 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 946 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 947 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 948 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 940 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 930 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 931 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 932 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 933 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 934 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 935 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 936 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 922 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 924 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 925 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 921 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 913 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 916 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 917 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 900 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 901 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 902 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 903 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 904 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 905 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 906 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 907 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 908 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 909 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 896 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 897 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 898 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 893 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 894 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 895 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 888 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 889 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 883 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 885 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 886 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 887 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 877 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 878 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 879 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 880 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 869 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 870 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 871 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 858 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 859 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 860 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 861 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 862 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 863 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 864 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 866 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 856 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 857 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 847 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 848 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 849 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 850 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 851 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 852 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 853 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 845 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 839 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 842 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 843 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 836 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 826 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 827 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 828 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 829 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 819 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 820 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 821 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 822 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 823 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 824 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 825 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 812 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 814 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 816 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 817 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 810 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 803 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 804 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 805 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 806 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 807 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 808 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 794 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 798 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 799 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 800 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 789 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 790 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 791 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 792 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 793 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 781 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 782 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 783 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 784 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 785 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 786 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 787 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 788 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 778 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 779 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 780 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 775 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 776 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 777 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 771 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 773 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 774 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 761 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 762 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 763 for peterclift123**@gmail.com  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 764 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 765 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 755 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 756 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 757 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 759 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 760 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 753 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 749 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 750 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 751 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 739 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 740 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 741 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 742 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 743 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 744 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 745 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 746 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 747 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 733 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 734 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 735 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 736 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 737 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 738 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 730 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 731 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 726 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 719 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 720 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 721 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 722 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 723 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 709 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 711 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 712 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 713 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 714 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 716 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 717 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 699 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 700 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 701 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 702 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 703 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 704 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 705 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 706 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 707 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 691 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 692 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 693 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 694 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 696 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 697 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 698 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 686 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 687 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 688 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 689 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 681 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 682 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 683 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 684 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 685 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 679 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 680 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 670 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 671 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 674 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 675 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 665 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 666 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 669 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 661 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 662 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 663 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 653 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 654 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 655 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 656 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 650 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 651 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 652 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 647 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 642 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 636 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 638 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 639 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 640 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 625 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 626 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 627 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 628 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 629 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 630 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 631 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 623 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 617 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 619 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 620 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 621 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 614 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 615 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 601 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 603 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 604 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 605 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 606 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 607 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 595 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 600 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 592 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 593 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 594 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 582 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 583 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 584 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 585 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 586 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 587 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 588 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 589 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 574 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 575 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 576 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 577 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 578 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 579 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 580 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 567 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 568 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 569 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 570 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 571 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 572 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 562 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 563 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 548 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 550 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 552 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 554 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 555 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 556 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 543 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 544 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 545 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 546 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 547 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 531 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 532 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 533 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 534 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 535 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 536 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 537 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 538 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 539 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 540 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 541 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 542 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 525 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 526 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 527 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 528 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 529 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 520 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 521 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 522 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 523 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 519 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 514 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 515 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 517 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 509 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 511 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 512 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 513 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 501 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 502 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 503 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 504 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 505 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 506 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 490 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 491 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 492 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 493 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 494 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 495 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 496 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 497 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 498 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 499 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 500 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 486 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 487 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 489 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 480 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 481 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 482 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 483 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 473 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 474 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 468 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 469 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 454 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 455 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 456 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 458 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 459 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 460 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 461 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 462 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 463 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 464 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 448 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 449 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 450 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 451 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 452 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 445 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 446 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 447 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 444 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 443 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 438 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 440 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 441 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 435 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 428 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 429 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 430 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 431 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 432 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 421 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 422 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 423 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 424 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 425 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 426 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 414 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 415 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 416 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 417 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 419 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 410 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 411 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 407 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 408 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 401 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 390 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 391 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 392 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 394 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 386 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 388 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 376 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 377 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 378 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 379 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 380 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 381 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 382 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 383 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 384 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 385 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 366 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 367 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 368 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 369 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 370 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 371 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 372 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 373 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 374 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 358 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 359 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 360 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 362 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 364 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 355 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 356 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 349 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 350 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 351 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 344 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 338 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 341 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 328 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 329 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 330 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 331 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 332 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 316 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 317 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 318 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 319 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 320 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 321 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 322 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 323 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 324 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 325 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 310 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 311 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 312 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 313 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 303 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 304 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 306 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 299 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 301 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 297 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 298 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 287 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 288 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 282 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 283 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 284 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 285 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 278 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 279 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 280 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 281 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 274 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 275 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 271 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 272 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 263 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 266 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 249 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 252 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 254 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 256 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 247 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 244 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 245 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 237 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 243 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 230 for <EMAIL>  
[2025-07-22 17:43:57] local.INFO: DRY RUN: Would migrate consultation ID 231 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 229 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 221 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 224 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 225 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 226 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 217 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 218 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 219 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 216 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 255 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 215 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 213 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 214 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 210 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 212 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 205 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 206 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 207 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 208 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 200 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 201 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 202 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 203 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 194 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 189 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 191 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 192 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 193 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 182 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 176 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 173 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 174 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 167 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 168 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 169 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 163 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 164 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 165 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 166 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 160 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 161 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 154 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 150 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 151 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 152 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 139 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 140 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 141 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 138 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 133 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 137 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 127 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 128 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 123 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 122 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: DRY RUN: Would migrate consultation ID 121 for <EMAIL>  
[2025-07-22 17:43:58] local.INFO: Clinic 14 completed: 546 processed, 0 skipped, 0 errors  
[2025-07-22 17:43:58] local.INFO: Migration completed - Processed: 546, Skipped: 0, Errors: 0  
[2025-07-22 17:43:58] local.INFO: Migration command completed successfully  
[2025-07-22 17:57:15] local.INFO: Starting migration command: migratewp:consultations  
[2025-07-22 17:57:15] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 17:57:22] local.INFO: WordPress API connection validated  
[2025-07-22 17:57:22] local.INFO: Database connection validated  
[2025-07-22 17:57:22] local.INFO: Starting consultations migration  
[2025-07-22 17:57:22] local.INFO: Running in DRY RUN mode  
[2025-07-22 17:57:22] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 17:57:29] local.INFO: Starting consultations migration for clinic 40  
[2025-07-22 17:57:29] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:57:37] local.INFO: Found 6 consultations for clinic 40  
[2025-07-22 17:57:37] local.INFO: DRY RUN: Would migrate consultation ID 677 for <EMAIL>  
[2025-07-22 17:57:37] local.INFO: DRY RUN: Would migrate consultation ID 645 for <EMAIL>  
[2025-07-22 17:57:37] local.INFO: DRY RUN: Would migrate consultation ID 644 for <EMAIL>  
[2025-07-22 17:57:37] local.INFO: DRY RUN: Would migrate consultation ID 609 for <EMAIL>  
[2025-07-22 17:57:37] local.INFO: DRY RUN: Would migrate consultation ID 610 for <EMAIL>  
[2025-07-22 17:57:37] local.INFO: DRY RUN: Would migrate consultation ID 611 for <EMAIL>  
[2025-07-22 17:57:37] local.INFO: Clinic 40 completed: 6 processed, 0 skipped, 0 errors  
[2025-07-22 17:57:37] local.INFO: Starting consultations migration for clinic 27  
[2025-07-22 17:57:37] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:57:41] local.INFO: No consultations found for clinic 27  
[2025-07-22 17:57:41] local.INFO: Starting consultations migration for clinic 26  
[2025-07-22 17:57:41] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:57:46] local.INFO: No consultations found for clinic 26  
[2025-07-22 17:57:46] local.INFO: Starting consultations migration for clinic 22  
[2025-07-22 17:57:46] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:57:52] local.INFO: No consultations found for clinic 22  
[2025-07-22 17:57:52] local.INFO: Starting consultations migration for clinic 16  
[2025-07-22 17:57:52] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:57:56] local.INFO: Found 13 consultations for clinic 16  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 616 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 558 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 524 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 516 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 478 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 363 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 354 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 345 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 346 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 334 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 340 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 240 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: DRY RUN: Would migrate consultation ID 234 for <EMAIL>  
[2025-07-22 17:57:56] local.INFO: Clinic 16 completed: 13 processed, 0 skipped, 0 errors  
[2025-07-22 17:57:56] local.INFO: Starting consultations migration for clinic 15  
[2025-07-22 17:57:56] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:00] local.INFO: No consultations found for clinic 15  
[2025-07-22 17:58:00] local.INFO: Starting consultations migration for clinic 14  
[2025-07-22 17:58:00] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:05] local.INFO: Found 546 consultations for clinic 14  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 975 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 976 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 977 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 978 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 972 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 973 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 974 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 962 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 964 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 965 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 966 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 967 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 968 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 969 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 970 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 958 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 959 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 961 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 955 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 956 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 957 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 949 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 950 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 951 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 943 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 944 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 945 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 946 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 947 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 948 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 940 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 930 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 931 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 932 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 933 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 934 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 935 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 936 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 922 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 924 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 925 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 921 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 913 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 916 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 917 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 900 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 901 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 902 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 903 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 904 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 905 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 906 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 907 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 908 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 909 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 896 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 897 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 898 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 893 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 894 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 895 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 888 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 889 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 883 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 885 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 886 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 887 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 877 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 878 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 879 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 880 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 869 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 870 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 871 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 858 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 859 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 860 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 861 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 862 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 863 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 864 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 866 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 856 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 857 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 847 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 848 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 849 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 850 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 851 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 852 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 853 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 845 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 839 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 842 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 843 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 836 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 826 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 827 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 828 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 829 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 819 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 820 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 821 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 822 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 823 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 824 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 825 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 812 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 814 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 816 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 817 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 810 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 803 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 804 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 805 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 806 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 807 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 808 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 794 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 798 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 799 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 800 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 789 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 790 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 791 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 792 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 793 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 781 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 782 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 783 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 784 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 785 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 786 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 787 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 788 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 778 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 779 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 780 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 775 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 776 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 777 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 771 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 773 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 774 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 761 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 762 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 763 for peterclift123**@gmail.com  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 764 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 765 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 755 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 756 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 757 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 759 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 760 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 753 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 749 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 750 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 751 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 739 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 740 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 741 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 742 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 743 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 744 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 745 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 746 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 747 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 733 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 734 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 735 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 736 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 737 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 738 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 730 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 731 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 726 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 719 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 720 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 721 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 722 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 723 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 709 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 711 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 712 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 713 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 714 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 716 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 717 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 699 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 700 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 701 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 702 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 703 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 704 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 705 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 706 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 707 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 691 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 692 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 693 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 694 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 696 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 697 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 698 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 686 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 687 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 688 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 689 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 681 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 682 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 683 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 684 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 685 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 679 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 680 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 670 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 671 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 674 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 675 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 665 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 666 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 669 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 661 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 662 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 663 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 653 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 654 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 655 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 656 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 650 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 651 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 652 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 647 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 642 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 636 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 638 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 639 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 640 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 625 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 626 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 627 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 628 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 629 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 630 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 631 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 623 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 617 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 619 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 620 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 621 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 614 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 615 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 601 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 603 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 604 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 605 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 606 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 607 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 595 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 600 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 592 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 593 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 594 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 582 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 583 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 584 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 585 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 586 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 587 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 588 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 589 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 574 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 575 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 576 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 577 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 578 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 579 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 580 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 567 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 568 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 569 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 570 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 571 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 572 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 562 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 563 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 548 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 550 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 552 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 554 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 555 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 556 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 543 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 544 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 545 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 546 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 547 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 531 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 532 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 533 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 534 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 535 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 536 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 537 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 538 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 539 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 540 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 541 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 542 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 525 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 526 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 527 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 528 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 529 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 520 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 521 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 522 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 523 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 519 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 514 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 515 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 517 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 509 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 511 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 512 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 513 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 501 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 502 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 503 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 504 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 505 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 506 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 490 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 491 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 492 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 493 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 494 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 495 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 496 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 497 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 498 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 499 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 500 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 486 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 487 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 489 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 480 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 481 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 482 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 483 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 473 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 474 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 468 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 469 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 454 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 455 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 456 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 458 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 459 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 460 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 461 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 462 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 463 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 464 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 448 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 449 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 450 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 451 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 452 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 445 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 446 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 447 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 444 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 443 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 438 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 440 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 441 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 435 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 428 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 429 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 430 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 431 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 432 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 421 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 422 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 423 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 424 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 425 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 426 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 414 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 415 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 416 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 417 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 419 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 410 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 411 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 407 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 408 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 401 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 390 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 391 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 392 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 394 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 386 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 388 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 376 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 377 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 378 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 379 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 380 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 381 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 382 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 383 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 384 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 385 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 366 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 367 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 368 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 369 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 370 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 371 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 372 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 373 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 374 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 358 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 359 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 360 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 362 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 364 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 355 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 356 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 349 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 350 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 351 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 344 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 338 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 341 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 328 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 329 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 330 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 331 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 332 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 316 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 317 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 318 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 319 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 320 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 321 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 322 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 323 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 324 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 325 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 310 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 311 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 312 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 313 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 303 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 304 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 306 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 299 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 301 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 297 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 298 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 287 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 288 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 282 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 283 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 284 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 285 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 278 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 279 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 280 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 281 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 274 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 275 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 271 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 272 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 263 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 266 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 249 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 252 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 254 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 256 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 247 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 244 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 245 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 237 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 243 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 230 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 231 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 229 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 221 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 224 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 225 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 226 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 217 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 218 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 219 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 216 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 255 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 215 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 213 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 214 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 210 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 212 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 205 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 206 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 207 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 208 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 200 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 201 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 202 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 203 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 194 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 189 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 191 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 192 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 193 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 182 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 176 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 173 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 174 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 167 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 168 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 169 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 163 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 164 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 165 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 166 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 160 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 161 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 154 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 150 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 151 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 152 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 139 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 140 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 141 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 138 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 133 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 137 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 127 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 128 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 123 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 122 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: DRY RUN: Would migrate consultation ID 121 for <EMAIL>  
[2025-07-22 17:58:05] local.INFO: Clinic 14 completed: 546 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:05] local.INFO: Starting consultations migration for clinic 13  
[2025-07-22 17:58:05] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:10] local.INFO: Found 1 consultations for clinic 13  
[2025-07-22 17:58:10] local.INFO: DRY RUN: Would migrate consultation ID 26 for   
[2025-07-22 17:58:10] local.INFO: Clinic 13 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:10] local.INFO: Starting consultations migration for clinic 12  
[2025-07-22 17:58:10] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:15] local.INFO: Found 24 consultations for clinic 12  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 938 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 939 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 941 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 914 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 892 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 872 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 834 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 838 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 772 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 667 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 657 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 646 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 618 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 612 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 602 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 598 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 375 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 357 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 339 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 326 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 270 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 238 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 239 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: DRY RUN: Would migrate consultation ID 195 for <EMAIL>  
[2025-07-22 17:58:15] local.INFO: Clinic 12 completed: 24 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:15] local.INFO: Starting consultations migration for clinic 11  
[2025-07-22 17:58:15] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:21] local.INFO: Found 278 consultations for clinic 11  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 971 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 960 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 953 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 952 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 942 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 937 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 926 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 927 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 928 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 929 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 910 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 911 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 912 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 918 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 919 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 920 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 899 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 890 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 891 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 882 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 884 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 915 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 873 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 874 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 875 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 876 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 881 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 865 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 867 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 868 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 854 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 855 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 846 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 840 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 841 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 844 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 831 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 832 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 833 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 835 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 837 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 830 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 818 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 811 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 809 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 795 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 796 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 797 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 801 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 802 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 766 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 758 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 754 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 752 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 748 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 732 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 727 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 728 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 729 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 718 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 708 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 710 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 715 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 695 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 690 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 725 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 676 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 664 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 668 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 658 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 659 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 660 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 648 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 634 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 635 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 637 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 641 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 632 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 633 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 624 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 622 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 608 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 596 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 597 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 599 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 591 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 581 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 573 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 564 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 565 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 566 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 549 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 551 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 557 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 530 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 518 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 508 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 507 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 488 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 485 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 472 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 475 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 467 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 471 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 465 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 466 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 453 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 470 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 442 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 436 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 433 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 434 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 427 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 412 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 406 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 400 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 402 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 403 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 404 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 405 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 395 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 396 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 365 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 352 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 353 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 347 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 335 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 336 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 337 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 342 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 343 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 333 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 327 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 314 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 315 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 302 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 296 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 289 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 290 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 291 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 292 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 293 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 294 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 295 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 286 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 276 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 277 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 273 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 268 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 269 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 258 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 259 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 260 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 261 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 262 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 265 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 250 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 251 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 257 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 267 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 235 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 236 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 232 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 233 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 228 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 222 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 223 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 204 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 209 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 197 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 199 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 198 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 183 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 184 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 185 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 186 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 178 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 179 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 170 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 171 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 172 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 175 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 180 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 162 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 156 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 149 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 153 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 143 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 144 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 145 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 146 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 148 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 135 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 136 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 131 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 132 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 129 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 125 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 126 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 130 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 124 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 119 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 120 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 108 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 109 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 110 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 111 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 103 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 104 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 114 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 102 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 101 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 97 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 98 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 90 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 91 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 93 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 94 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 96 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 88 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 89 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 86 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 87 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 92 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 83 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 84 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 85 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 70 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 72 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 73 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 74 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 75 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 76 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 77 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 78 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 67 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 68 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 69 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 79 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 63 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 64 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 65 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 60 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 61 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 62 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 59 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 56 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 57 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 55 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 53 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 50 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 52 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 42 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 43 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 44 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 33 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 34 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 35 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 36 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 37 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 46 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 29 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 30 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 31 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 25 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 24 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 22 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 23 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 18 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 19 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 20 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 15 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 16 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 14 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 7 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 9 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 10 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: DRY RUN: Would migrate consultation ID 12 for <EMAIL>  
[2025-07-22 17:58:21] local.INFO: Clinic 11 completed: 278 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:21] local.INFO: Starting consultations migration for clinic 10  
[2025-07-22 17:58:21] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:25] local.INFO: Found 1 consultations for clinic 10  
[2025-07-22 17:58:25] local.INFO: DRY RUN: Would migrate consultation ID 81 for <EMAIL>  
[2025-07-22 17:58:25] local.INFO: Clinic 10 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:25] local.INFO: Starting consultations migration for clinic 9  
[2025-07-22 17:58:25] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:29] local.INFO: Found 1 consultations for clinic 9  
[2025-07-22 17:58:29] local.INFO: DRY RUN: Would migrate consultation ID 13 for <EMAIL>  
[2025-07-22 17:58:29] local.INFO: Clinic 9 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:29] local.INFO: Starting consultations migration for clinic 8  
[2025-07-22 17:58:29] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:34] local.INFO: Found 4 consultations for clinic 8  
[2025-07-22 17:58:34] local.INFO: DRY RUN: Would migrate consultation ID 389 for <EMAIL>  
[2025-07-22 17:58:34] local.INFO: DRY RUN: Would migrate consultation ID 246 for <EMAIL>  
[2025-07-22 17:58:34] local.INFO: DRY RUN: Would migrate consultation ID 188 for <EMAIL>  
[2025-07-22 17:58:34] local.INFO: DRY RUN: Would migrate consultation ID 51 for <EMAIL>  
[2025-07-22 17:58:34] local.INFO: Clinic 8 completed: 4 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:34] local.INFO: Starting consultations migration for clinic 6  
[2025-07-22 17:58:34] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:37] local.INFO: No consultations found for clinic 6  
[2025-07-22 17:58:37] local.INFO: Starting consultations migration for clinic 5  
[2025-07-22 17:58:37] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:42] local.INFO: Found 39 consultations for clinic 5  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 963 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 923 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 767 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 768 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 770 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 678 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 769 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 559 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 560 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 561 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 479 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 477 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 437 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 439 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 476 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 409 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 397 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 398 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 399 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 308 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 307 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 309 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 227 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 220 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 181 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 187 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 142 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 112 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 82 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 47 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 48 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 49 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 71 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 38 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 21 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 39 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 40 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 41 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: DRY RUN: Would migrate consultation ID 32 for <EMAIL>  
[2025-07-22 17:58:42] local.INFO: Clinic 5 completed: 39 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:42] local.INFO: Starting consultations migration for clinic 4  
[2025-07-22 17:58:42] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:46] local.INFO: No consultations found for clinic 4  
[2025-07-22 17:58:46] local.INFO: Starting consultations migration for clinic 2  
[2025-07-22 17:58:46] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:50] local.INFO: No consultations found for clinic 2  
[2025-07-22 17:58:50] local.INFO: Starting consultations migration for clinic 1  
[2025-07-22 17:58:50] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 17:58:54] local.INFO: Found 9 consultations for clinic 1  
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 590 for <EMAIL>  
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 484 for <EMAIL>  
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 253 for <EMAIL>  
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 177 for <EMAIL>  
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 155 for <EMAIL>  
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 80 for <EMAIL>  
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 54 for   
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 27 for   
[2025-07-22 17:58:54] local.INFO: DRY RUN: Would migrate consultation ID 28 for   
[2025-07-22 17:58:54] local.INFO: Clinic 1 completed: 9 processed, 0 skipped, 0 errors  
[2025-07-22 17:58:54] local.INFO: Migration completed - Processed: 922, Skipped: 0, Errors: 0  
[2025-07-22 17:58:54] local.INFO: Migration command completed successfully  
[2025-07-22 18:08:49] local.INFO: Starting migration command: migratewp:consultations  
[2025-07-22 18:08:49] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 18:08:54] local.INFO: WordPress API connection validated  
[2025-07-22 18:08:54] local.INFO: Database connection validated  
[2025-07-22 18:08:54] local.INFO: Starting consultations migration  
[2025-07-22 18:08:54] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 18:08:58] local.INFO: Starting consultations migration for clinic 40  
[2025-07-22 18:08:58] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:02] local.INFO: Found 6 consultations for clinic 40  
[2025-07-22 18:09:02] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 6 errors  
[2025-07-22 18:09:02] local.INFO: Starting consultations migration for clinic 27  
[2025-07-22 18:09:02] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:06] local.INFO: No consultations found for clinic 27  
[2025-07-22 18:09:06] local.INFO: Starting consultations migration for clinic 26  
[2025-07-22 18:09:06] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:09] local.INFO: No consultations found for clinic 26  
[2025-07-22 18:09:09] local.INFO: Starting consultations migration for clinic 22  
[2025-07-22 18:09:09] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:13] local.INFO: No consultations found for clinic 22  
[2025-07-22 18:09:13] local.INFO: Starting consultations migration for clinic 16  
[2025-07-22 18:09:13] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:16] local.INFO: Found 13 consultations for clinic 16  
[2025-07-22 18:09:16] local.INFO: Clinic 16 completed: 0 processed, 0 skipped, 13 errors  
[2025-07-22 18:09:16] local.INFO: Starting consultations migration for clinic 15  
[2025-07-22 18:09:16] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:21] local.INFO: No consultations found for clinic 15  
[2025-07-22 18:09:21] local.INFO: Starting consultations migration for clinic 14  
[2025-07-22 18:09:21] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:26] local.INFO: Found 546 consultations for clinic 14  
[2025-07-22 18:09:27] local.INFO: Clinic 14 completed: 0 processed, 0 skipped, 546 errors  
[2025-07-22 18:09:27] local.INFO: Starting consultations migration for clinic 13  
[2025-07-22 18:09:27] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:31] local.INFO: Found 1 consultations for clinic 13  
[2025-07-22 18:09:31] local.INFO: Clinic 13 completed: 0 processed, 1 skipped, 0 errors  
[2025-07-22 18:09:31] local.INFO: Starting consultations migration for clinic 12  
[2025-07-22 18:09:31] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:35] local.INFO: Found 24 consultations for clinic 12  
[2025-07-22 18:09:35] local.INFO: Clinic 12 completed: 0 processed, 0 skipped, 24 errors  
[2025-07-22 18:09:35] local.INFO: Starting consultations migration for clinic 11  
[2025-07-22 18:09:35] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:39] local.INFO: Found 278 consultations for clinic 11  
[2025-07-22 18:09:40] local.INFO: Clinic 11 completed: 0 processed, 0 skipped, 278 errors  
[2025-07-22 18:09:40] local.INFO: Starting consultations migration for clinic 10  
[2025-07-22 18:09:40] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:44] local.INFO: Found 1 consultations for clinic 10  
[2025-07-22 18:09:44] local.INFO: Clinic 10 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 18:09:44] local.INFO: Starting consultations migration for clinic 9  
[2025-07-22 18:09:44] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:48] local.INFO: Found 1 consultations for clinic 9  
[2025-07-22 18:09:48] local.INFO: Clinic 9 completed: 0 processed, 0 skipped, 1 errors  
[2025-07-22 18:09:48] local.INFO: Starting consultations migration for clinic 8  
[2025-07-22 18:09:48] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:52] local.INFO: Found 4 consultations for clinic 8  
[2025-07-22 18:09:52] local.INFO: Clinic 8 completed: 0 processed, 0 skipped, 4 errors  
[2025-07-22 18:09:52] local.INFO: Starting consultations migration for clinic 6  
[2025-07-22 18:09:52] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:56] local.INFO: No consultations found for clinic 6  
[2025-07-22 18:09:56] local.INFO: Starting consultations migration for clinic 5  
[2025-07-22 18:09:56] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:09:59] local.INFO: Found 39 consultations for clinic 5  
[2025-07-22 18:09:59] local.INFO: Clinic 5 completed: 0 processed, 0 skipped, 39 errors  
[2025-07-22 18:09:59] local.INFO: Starting consultations migration for clinic 4  
[2025-07-22 18:09:59] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:10:03] local.INFO: No consultations found for clinic 4  
[2025-07-22 18:10:03] local.INFO: Starting consultations migration for clinic 2  
[2025-07-22 18:10:03] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:10:06] local.INFO: No consultations found for clinic 2  
[2025-07-22 18:10:06] local.INFO: Starting consultations migration for clinic 1  
[2025-07-22 18:10:06] local.INFO: Making KiviCare API request: laravel_get_clinic_encounters  
[2025-07-22 18:10:10] local.INFO: Found 9 consultations for clinic 1  
[2025-07-22 18:10:10] local.INFO: Clinic 1 completed: 0 processed, 3 skipped, 6 errors  
[2025-07-22 18:10:10] local.INFO: Migration completed - Processed: 0, Skipped: 4, Errors: 918  
[2025-07-22 18:10:10] local.INFO: Migration command completed successfully  
