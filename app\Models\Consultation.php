<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Consultation extends Model
{
    use HasFactory;

    protected $fillable = [
        'appointment_id',
        'patient_id',
        'provider_id',
        'clinic_id',
        'consultation_type',
        'status',
        'consultation_date',
        'duration_minutes',
        'consultation_mode',
        'description',
        'added_by_wp_id',
        'is_telemedicine',
        'vital_signs',
        'main_tabs',
        'additional_tabs',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'consultation_date' => 'datetime',
        'vital_signs' => 'array',
        'main_tabs' => 'array',
        'additional_tabs' => 'array',
        'is_telemedicine' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the appointment associated with the consultation.
     */
    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    /**
     * Get the patient associated with the consultation.
     */
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    /**
     * Get the provider associated with the consultation.
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the clinic associated with the consultation.
     */
    public function clinic()
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the consultation notes.
     */
    public function notes()
    {
        return $this->hasMany(ConsultationNote::class);
    }

    /**
     * Get the diagnoses for this consultation.
     */
    public function diagnoses()
    {
        return $this->hasMany(Diagnosis::class);
    }

    /**
     * Get the treatment plans for this consultation.
     */
    public function treatmentPlans()
    {
        return $this->hasMany(TreatmentPlan::class);
    }

    /**
     * Get the prescriptions for this consultation.
     */
    public function prescriptions()
    {
        return $this->hasMany(ConsultationPrescription::class);
    }

    /**
     * Get the documents for this consultation.
     */
    public function documents()
    {
        return $this->hasMany(ConsultationDocument::class);
    }

    /**
     * Get the medical letters for this consultation.
     */
    public function medicalLetters()
    {
        return $this->hasMany(MedicalLetter::class);
    }

    /**
     * Get the consultation files for this consultation.
     */
    public function files()
    {
        return $this->hasMany(ConsultationFile::class);
    }

    /**
     * Get the consultation tabs for this consultation.
     */
    public function tabs()
    {
        return $this->hasMany(ConsultationTab::class)->ordered();
    }

    /**
     * Get the vital signs for this consultation.
     */
    public function vitals()
    {
        return $this->hasMany(ConsultationVital::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get the services performed during the consultation.
     */
    public function consultationServices()
    {
        return $this->hasMany(ConsultationService::class);
    }

    /**
     * Get billable services for the consultation.
     */
    public function billableServices()
    {
        return $this->hasMany(ConsultationService::class)->billable();
    }

    /**
     * Get unbilled services for the consultation.
     */
    public function unbilledServices()
    {
        return $this->hasMany(ConsultationService::class)->unbilled();
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by consultation type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('consultation_type', $type);
    }

    /**
     * Scope to filter by provider.
     */
    public function scopeByProvider($query, $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Scope to filter by patient.
     */
    public function scopeByPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('consultation_date', [$startDate, $endDate]);
    }

    /**
     * Check if consultation is completed.
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if consultation is in progress.
     */
    public function isInProgress()
    {
        return $this->status === 'in_progress';
    }

    /**
     * Get the duration in human readable format.
     */
    public function getDurationDisplayAttribute()
    {
        if (!$this->duration_minutes) {
            return null;
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Get the primary diagnosis.
     */
    public function getPrimaryDiagnosis()
    {
        return $this->diagnoses()->where('type', 'primary')->first();
    }

    /**
     * Get all active prescriptions from this consultation.
     */
    public function getActivePrescriptions()
    {
        return $this->prescriptions()->where('status', 'active')->get();
    }
}
