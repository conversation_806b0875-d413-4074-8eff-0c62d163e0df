<?php

namespace App\Console\Commands\Migration;

use App\Models\Consultation;
use App\Models\Appointment;
use App\Models\User;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Consultations Command
 * 
 * Simple focused migration for consultations/encounters only
 */
class MigrateConsultations extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:consultations
                            {--clinic= : Specific clinic ID, comma-separated IDs, or "all" for all clinics}
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate consultations/encounters from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for consultations migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/consultations");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.consultations_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/consultations_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.consultations_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/consultations_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'consultations_migration';
        $this->errorLogChannel = 'consultations_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        $this->info("=== MIGRATING CONSULTATIONS ===");
        $this->logInfo("Starting consultations migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        $clinicOption = $this->option('clinic') ?? 'all';
        $clinicIds = $this->parseClinicOption($clinicOption);

        $totalProcessed = 0;
        $totalSkipped = 0;
        $totalErrors = 0;
        $failedClinics = [];

        foreach ($clinicIds as $clinicId) {
            $this->info("\n--- Migrating consultations for clinic ID: {$clinicId} ---");
            $this->logInfo("Starting consultations migration for clinic {$clinicId}");
            
            try {
                // 1. Make API call - get consultations/encounters for this clinic
                $response = $this->makeApiRequest('laravel_get_clinic_encounters', ['clinic_id' => $clinicId]);
                $wpEncounters = $response['data'] ?? [];

                if (empty($wpEncounters)) {
                    $this->info("No consultations found for clinic {$clinicId}");
                    $this->logInfo("No consultations found for clinic {$clinicId}");
                    continue;
                }

                $this->info("Found " . count($wpEncounters) . " consultations for clinic {$clinicId}");
                $this->logInfo("Found " . count($wpEncounters) . " consultations for clinic {$clinicId}");

                $processed = 0;
                $skipped = 0;
                $errors = 0;

                // 2. Process each consultation
                foreach ($wpEncounters as $wpEncounter) {
                    try {
                        // Find patient by email (your email-based approach)
                        $patientEmail = $wpEncounter['related_users']['patient_email'] ?? $wpEncounter['patient']['email'] ?? null;
                        $providerEmail = $wpEncounter['related_users']['doctor_email'] ?? $wpEncounter['doctor']['email'] ?? null;
                        
                        if ($this->isDryRun()) {
                            $encounterDate = $wpEncounter['encounter_date'] ?? 'Unknown';
                            $this->info("Would migrate consultation: {$patientEmail} → {$providerEmail} on {$encounterDate}");
                            $this->logInfo("DRY RUN: Would migrate consultation ID {$wpEncounter['id']} for {$patientEmail}");
                            $processed++;
                            continue;
                        }

                        if (!$patientEmail) {
                            $this->error("Skipped consultation ID {$wpEncounter['id']}: No patient email");
                            $this->logError("Skipped consultation ID {$wpEncounter['id']}: No patient email", $wpEncounter);
                            $skipped++;
                            continue;
                        }

                        $patient = User::where('email', $patientEmail)->first();
                        if (!$patient) {
                            $this->error("Skipped consultation ID {$wpEncounter['id']}: Patient not found ({$patientEmail})");
                            $this->logError("Skipped consultation ID {$wpEncounter['id']}: Patient not found", ['email' => $patientEmail]);
                            $skipped++;
                            continue;
                        }

                        // Find provider by email
                        $provider = $providerEmail ? User::where('email', $providerEmail)->first() : null;

                        // Find related appointment if exists
                        $appointment = null;
                        if (isset($wpEncounter['appointment_id'])) {
                            $appointment = Appointment::where('wp_appointment_id', $wpEncounter['appointment_id'])->first();
                        }

                        // Map WordPress clinic ID to Laravel clinic ID
                        $laravelClinicId = $this->mapWordPressClinicToLaravel($clinicId);
                        if (!$laravelClinicId) {
                            $this->error("✗ Laravel clinic not found for WordPress clinic ID {$clinicId}");
                            $this->logError("Laravel clinic not found for WordPress clinic ID {$clinicId}");
                            $skipped++;
                            continue;
                        }

                        // Transform WordPress encounter data to Laravel consultation format
                        $laravelData = $this->transformer->transformConsultationSimple($wpEncounter);
                        $laravelData['patient_id'] = $patient->id;
                        $laravelData['provider_id'] = $provider ? $provider->id : null;
                        $laravelData['appointment_id'] = $appointment ? $appointment->id : null;
                        $laravelData['clinic_id'] = $laravelClinicId;
                        $laravelData['wp_encounter_id'] = $wpEncounter['id'];

                        // Create or update consultation
                        $consultation = Consultation::updateOrCreate(
                            ['wp_encounter_id' => $wpEncounter['id']],
                            $laravelData
                        );

                        $this->info("✓ Migrated consultation: ID {$consultation->id} ({$patient->email} → " . ($provider ? $provider->email : 'No provider') . ")");
                        $this->logInfo("Successfully migrated consultation ID {$wpEncounter['id']} → Laravel ID {$consultation->id}");
                        $processed++;

                    } catch (Exception $e) {
                        $this->error("✗ Failed to migrate consultation ID {$wpEncounter['id']}: " . $e->getMessage());
                        $this->logError("Failed to migrate consultation ID {$wpEncounter['id']}: " . $e->getMessage(), $wpEncounter);
                        $errors++;
                    }
                }

                $this->info("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
                $this->logInfo("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");

                $totalProcessed += $processed;
                $totalSkipped += $skipped;
                $totalErrors += $errors;

            } catch (Exception $e) {
                $this->error("❌ Failed to process clinic {$clinicId}: " . $e->getMessage());
                $this->logError("Failed to process clinic {$clinicId}: " . $e->getMessage());
                $failedClinics[] = $clinicId;
                continue;
            }
        }

        // 3. Generate summary
        $this->generateSummary($totalProcessed, $totalSkipped, $totalErrors, $failedClinics);

        return 0;
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors, $failedClinics)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 CONSULTATIONS MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");
        
        if (!empty($failedClinics)) {
            $this->error("🚨 Failed Clinics: " . implode(', ', $failedClinics));
        }
        
        if ($errors > 0 || !empty($failedClinics)) {
            $this->error("\n⚠️  Some consultations failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 All consultations migrated successfully!");
        }
        
        $this->info("📁 Logs saved to: storage/logs/migration/consultations/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
        if (!empty($failedClinics)) {
            $this->logError("Failed clinics: " . implode(', ', $failedClinics));
        }
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }

    /**
     * Map WordPress clinic ID to Laravel clinic ID
     */
    protected function mapWordPressClinicToLaravel($wpClinicId)
    {
        // Find Laravel clinic by wp_clinic_id
        $clinic = \App\Models\Clinic::where('wp_clinic_id', $wpClinicId)->first();

        if (!$clinic) {
            return null;
        }

        return $clinic->id;
    }
}
