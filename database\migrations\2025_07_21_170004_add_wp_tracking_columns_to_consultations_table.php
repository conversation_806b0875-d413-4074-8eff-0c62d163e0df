<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consultations', function (Blueprint $table) {
            $table->integer('wp_encounter_id')->nullable()->unique()->after('id');
            $table->text('description')->nullable()->after('consultation_mode');
            $table->integer('added_by_wp_id')->nullable()->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consultations', function (Blueprint $table) {
            $table->dropColumn(['wp_encounter_id', 'description', 'added_by_wp_id']);
        });
    }
};
